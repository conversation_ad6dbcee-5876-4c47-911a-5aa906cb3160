
import { useState } from "react";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { Calendar } from "@/components/ui/calendar";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { format } from "date-fns";
import {
  CalendarClock,
  CalendarDays,
  CalendarPlus,
  CalendarCheck,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Sample events data
const dummyEvents = [
  {
    id: 1,
    title: "Final Project Presentation",
    date: new Date(2025, 3, 15),
    type: "academic",
    description: "Present your final project to the class",
    location: "Room 301",
  },
  {
    id: 2,
    title: "Mathematics Exam",
    date: new Date(2025, 3, 18),
    type: "exam",
    description: "Mid-term examination",
    location: "Examination Hall",
  },
  {
    id: 3,
    title: "Student Council Meeting",
    date: new Date(2025, 3, 20),
    type: "extracurricular",
    description: "Monthly meeting for student council members",
    location: "Conference Room",
  },
  {
    id: 4,
    title: "Programming Workshop",
    date: new Date(2025, 3, 25),
    type: "workshop",
    description: "Learn advanced programming techniques",
    location: "Computer Lab",
  },
  {
    id: 5,
    title: "Career Fair",
    date: new Date(2025, 3, 28),
    type: "event",
    description: "Annual career fair with industry representatives",
    location: "Main Hall",
  },
];

const CalendarPage = () => {
  const [date, setDate] = useState<Date | undefined>(new Date());
  const [events, setEvents] = useState(dummyEvents);
  const [isAddEventOpen, setIsAddEventOpen] = useState(false);
  const [viewMode, setViewMode] = useState("month");
  const [newEvent, setNewEvent] = useState({
    title: "",
    date: new Date(),
    type: "academic",
    description: "",
    location: "",
  });

  const handleDateSelect = (selectedDate: Date | undefined) => {
    if (selectedDate) {
      setDate(selectedDate);
    }
  };

  const eventsForSelectedDate = events.filter(
    (event) => date && event.date.toDateString() === date.toDateString()
  );

  const handleAddEvent = () => {
    const event = {
      id: events.length + 1,
      ...newEvent,
      date: date || new Date(),
    };
    setEvents([...events, event]);
    setNewEvent({
      title: "",
      date: new Date(),
      type: "academic",
      description: "",
      location: "",
    });
    setIsAddEventOpen(false);
  };

  const eventTypeBadgeColor = (type: string) => {
    switch (type) {
      case "academic":
        return "bg-blue-100 text-blue-800";
      case "exam":
        return "bg-red-100 text-red-800";
      case "workshop":
        return "bg-green-100 text-green-800";
      case "extracurricular":
        return "bg-purple-100 text-purple-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <DashboardLayout>
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Academic Calendar</h1>
        <p className="text-gray-600">
          Manage your schedule and track important academic dates
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle>Calendar</CardTitle>
              <CardDescription>Select a date to view events</CardDescription>
              <div className="flex justify-between mt-2 mb-2">
                <Button variant="outline" size="sm">
                  <ChevronLeft className="h-4 w-4 mr-1" /> Previous
                </Button>
                <span className="text-sm font-medium py-2">
                  {date ? format(date, "MMMM yyyy") : "Select a date"}
                </span>
                <Button variant="outline" size="sm">
                  Next <ChevronRight className="h-4 w-4 ml-1" />
                </Button>
              </div>
              <Tabs defaultValue="month" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="month" onClick={() => setViewMode("month")}>Month</TabsTrigger>
                  <TabsTrigger value="week" onClick={() => setViewMode("week")}>Week</TabsTrigger>
                  <TabsTrigger value="day" onClick={() => setViewMode("day")}>Day</TabsTrigger>
                </TabsList>
              </Tabs>
            </CardHeader>
            <CardContent>
              <Calendar
                mode="single"
                selected={date}
                onSelect={handleDateSelect}
                className="p-3 pointer-events-auto"
              />

              <div className="mt-4 space-y-2">
                <h3 className="font-medium text-sm">Event Types</h3>
                <div className="flex flex-wrap gap-2">
                  <span className="px-2 py-1 text-xs rounded bg-blue-100 text-blue-800">
                    Academic
                  </span>
                  <span className="px-2 py-1 text-xs rounded bg-red-100 text-red-800">
                    Exam
                  </span>
                  <span className="px-2 py-1 text-xs rounded bg-green-100 text-green-800">
                    Workshop
                  </span>
                  <span className="px-2 py-1 text-xs rounded bg-purple-100 text-purple-800">
                    Extracurricular
                  </span>
                </div>
              </div>

              <Dialog open={isAddEventOpen} onOpenChange={setIsAddEventOpen}>
                <DialogTrigger asChild>
                  <Button className="w-full mt-4">
                    <CalendarPlus className="mr-2 h-4 w-4" />
                    Add Event
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Add New Event</DialogTitle>
                    <DialogDescription>
                      Create a new event for {date ? format(date, "PPP") : "selected date"}
                    </DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid gap-2">
                      <Label htmlFor="event-title">Title</Label>
                      <Input
                        id="event-title"
                        value={newEvent.title}
                        onChange={(e) => setNewEvent({...newEvent, title: e.target.value})}
                        placeholder="Event title"
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="event-type">Event Type</Label>
                      <Select
                        value={newEvent.type}
                        onValueChange={(value) => setNewEvent({...newEvent, type: value})}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select event type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="academic">Academic</SelectItem>
                          <SelectItem value="exam">Exam</SelectItem>
                          <SelectItem value="workshop">Workshop</SelectItem>
                          <SelectItem value="extracurricular">Extracurricular</SelectItem>
                          <SelectItem value="event">Other Event</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="event-location">Location</Label>
                      <Input
                        id="event-location"
                        value={newEvent.location}
                        onChange={(e) => setNewEvent({...newEvent, location: e.target.value})}
                        placeholder="Event location"
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="event-description">Description</Label>
                      <Input
                        id="event-description"
                        value={newEvent.description}
                        onChange={(e) => setNewEvent({...newEvent, description: e.target.value})}
                        placeholder="Event description"
                      />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setIsAddEventOpen(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleAddEvent}>Add Event</Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </CardContent>
          </Card>
        </div>

        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>
                Events for {date ? format(date, "PPPP") : "Selected Date"}
              </CardTitle>
              <CardDescription>
                {eventsForSelectedDate.length} events scheduled
              </CardDescription>
            </CardHeader>
            <CardContent>
              {eventsForSelectedDate.length > 0 ? (
                <div className="space-y-4">
                  {eventsForSelectedDate.map((event) => (
                    <div
                      key={event.id}
                      className="border rounded-lg p-4 hover:shadow-md transition-shadow"
                    >
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="font-bold text-lg">{event.title}</h3>
                          <div className="flex items-center space-x-2 mt-1">
                            <span className={`px-2 py-1 text-xs rounded ${eventTypeBadgeColor(event.type)}`}>
                              {event.type.charAt(0).toUpperCase() + event.type.slice(1)}
                            </span>
                            <span className="text-sm text-gray-500 flex items-center">
                              <CalendarClock className="h-3 w-3 mr-1" />
                              {format(event.date, "h:mm a")}
                            </span>
                            {event.location && (
                              <span className="text-sm text-gray-500">
                                {event.location}
                              </span>
                            )}
                          </div>
                          <p className="text-sm mt-2">{event.description}</p>
                        </div>
                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm">
                            Edit
                          </Button>
                          <Dialog>
                            <DialogTrigger asChild>
                              <Button variant="outline" size="sm">
                                Details
                              </Button>
                            </DialogTrigger>
                            <DialogContent>
                              <DialogHeader>
                                <DialogTitle>{event.title}</DialogTitle>
                                <DialogDescription>
                                  {format(event.date, "PPPP")}
                                </DialogDescription>
                              </DialogHeader>
                              <div className="space-y-4">
                                <div>
                                  <h4 className="font-medium text-sm">Type</h4>
                                  <p className="mt-1">
                                    <span className={`px-2 py-1 text-xs rounded ${eventTypeBadgeColor(event.type)}`}>
                                      {event.type.charAt(0).toUpperCase() + event.type.slice(1)}
                                    </span>
                                  </p>
                                </div>
                                <div>
                                  <h4 className="font-medium text-sm">Location</h4>
                                  <p className="mt-1">{event.location}</p>
                                </div>
                                <div>
                                  <h4 className="font-medium text-sm">Description</h4>
                                  <p className="mt-1">{event.description}</p>
                                </div>
                              </div>
                              <DialogFooter>
                                <Button variant="outline">
                                  <CalendarCheck className="mr-2 h-4 w-4" /> RSVP
                                </Button>
                                <Button>
                                  Export to Calendar
                                </Button>
                              </DialogFooter>
                            </DialogContent>
                          </Dialog>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 border rounded-lg border-dashed">
                  <CalendarDays className="h-10 w-10 mx-auto text-gray-400 mb-2" />
                  <h3 className="font-medium text-lg mb-1">No events for this date</h3>
                  <p className="text-gray-500 mb-4">
                    Add a new event to start populating your calendar
                  </p>
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button>
                        <CalendarPlus className="mr-2 h-4 w-4" />
                        Add Event
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Add New Event</DialogTitle>
                        <DialogDescription>
                          Create a new event for {date ? format(date, "PPP") : "selected date"}
                        </DialogDescription>
                      </DialogHeader>
                      <div className="grid gap-4 py-4">
                        <div className="grid gap-2">
                          <Label htmlFor="event-title">Title</Label>
                          <Input
                            id="event-title"
                            value={newEvent.title}
                            onChange={(e) => setNewEvent({...newEvent, title: e.target.value})}
                            placeholder="Event title"
                          />
                        </div>
                        <div className="grid gap-2">
                          <Label htmlFor="event-type">Event Type</Label>
                          <Select
                            value={newEvent.type}
                            onValueChange={(value) => setNewEvent({...newEvent, type: value})}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select event type" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="academic">Academic</SelectItem>
                              <SelectItem value="exam">Exam</SelectItem>
                              <SelectItem value="workshop">Workshop</SelectItem>
                              <SelectItem value="extracurricular">Extracurricular</SelectItem>
                              <SelectItem value="event">Other Event</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="grid gap-2">
                          <Label htmlFor="event-location">Location</Label>
                          <Input
                            id="event-location"
                            value={newEvent.location}
                            onChange={(e) => setNewEvent({...newEvent, location: e.target.value})}
                            placeholder="Event location"
                          />
                        </div>
                        <div className="grid gap-2">
                          <Label htmlFor="event-description">Description</Label>
                          <Input
                            id="event-description"
                            value={newEvent.description}
                            onChange={(e) => setNewEvent({...newEvent, description: e.target.value})}
                            placeholder="Event description"
                          />
                        </div>
                      </div>
                      <DialogFooter>
                        <Button variant="outline" onClick={() => setIsAddEventOpen(false)}>
                          Cancel
                        </Button>
                        <Button onClick={handleAddEvent}>Add Event</Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>
              )}
            </CardContent>
          </Card>

          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Upcoming Events</CardTitle>
              <CardDescription>Your next 5 scheduled events</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {events
                  .filter((event) => event.date >= new Date())
                  .sort((a, b) => a.date.getTime() - b.date.getTime())
                  .slice(0, 5)
                  .map((event) => (
                    <div
                      key={event.id}
                      className="flex items-center border-b pb-3 last:border-0 last:pb-0"
                    >
                      <div className="min-w-16 text-center mr-4">
                        <p className="text-sm font-bold">{format(event.date, "d")}</p>
                        <p className="text-xs">{format(event.date, "MMM")}</p>
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium">{event.title}</h4>
                        <div className="flex items-center mt-1">
                          <span className={`px-2 py-0.5 text-xs rounded mr-2 ${eventTypeBadgeColor(event.type)}`}>
                            {event.type.charAt(0).toUpperCase() + event.type.slice(1)}
                          </span>
                          {event.location && (
                            <span className="text-xs text-gray-500">{event.location}</span>
                          )}
                        </div>
                      </div>
                      <Button variant="ghost" size="sm">
                        View
                      </Button>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default CalendarPage;
